import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [books, setBooks] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newBook, setNewBook] = useState({ title: '', author: '', status: 'to_read' });

  // Get backend URL from Choreo-injected config or fallback to localhost
  const getBackendUrl = () => {
    if (window.config && window.config.backendUrl) {
      return window.config.backendUrl;
    }
    return process.env.REACT_APP_API_URL || 'http://localhost:8080';
  };

  const backendUrl = getBackendUrl();

  // Load books on component mount
  useEffect(() => {
    loadBooks();
  }, []);

  const loadBooks = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${backendUrl}/reading-list/books`);
      if (!response.ok) {
        throw new Error('Failed to fetch books');
      }
      const booksData = await response.json();
      setBooks(booksData);
    } catch (err) {
      setError('Failed to load books. Please try again.');
      console.error('Error loading books:', err);
    } finally {
      setLoading(false);
    }
  };

  const addBook = async (e) => {
    e.preventDefault();
    if (!newBook.title || !newBook.author) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch(`${backendUrl}/reading-list/books`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newBook),
      });

      if (!response.ok) {
        throw new Error('Failed to add book');
      }

      await loadBooks();
      setNewBook({ title: '', author: '', status: 'to_read' });
      setShowAddForm(false);
      setError(null);
    } catch (err) {
      setError('Failed to add book. Please try again.');
      console.error('Error adding book:', err);
    }
  };

  const updateBookStatus = async (uuid, status) => {
    try {
      const response = await fetch(`${backendUrl}/reading-list/books/${uuid}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error('Failed to update book');
      }

      await loadBooks();
    } catch (err) {
      setError('Failed to update book. Please try again.');
      console.error('Error updating book:', err);
    }
  };

  const deleteBook = async (uuid) => {
    if (!window.confirm('Are you sure you want to delete this book?')) {
      return;
    }

    try {
      const response = await fetch(`${backendUrl}/reading-list/books/${uuid}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete book');
      }

      await loadBooks();
    } catch (err) {
      setError('Failed to delete book. Please try again.');
      console.error('Error deleting book:', err);
    }
  };

  const bookArray = Object.values(books || {});
  const groupedBooks = {
    reading: bookArray.filter(book => book.status === 'reading'),
    to_read: bookArray.filter(book => book.status === 'to_read'),
    read: bookArray.filter(book => book.status === 'read')
  };

  const statusConfig = {
    reading: { title: 'Currently Reading', icon: '📖', color: '#3498db' },
    to_read: { title: 'Want to Read', icon: '📋', color: '#f39c12' },
    read: { title: 'Completed', icon: '✅', color: '#27ae60' }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600">
      {/* Header */}
      <header className="bg-white bg-opacity-95 backdrop-blur-sm border-b border-white border-opacity-20 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">📚 My Reading List</h1>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-full font-semibold transition-all duration-200 hover:shadow-lg"
            >
              + Add Book
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Error Message */}
        {error && (
          <div className="bg-red-500 text-white p-4 rounded-lg mb-6 flex justify-between items-center">
            <span>{error}</span>
            <button onClick={() => setError(null)} className="text-white hover:text-gray-200">
              ✕
            </button>
          </div>
        )}

        {/* Add Book Form */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-gray-800">📚 Add New Book</h3>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ✕
                </button>
              </div>
              <form onSubmit={addBook}>
                <div className="mb-4">
                  <label className="block text-gray-700 font-semibold mb-2">Book Title *</label>
                  <input
                    type="text"
                    value={newBook.title}
                    onChange={(e) => setNewBook({ ...newBook, title: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter book title"
                    required
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-gray-700 font-semibold mb-2">Author *</label>
                  <input
                    type="text"
                    value={newBook.author}
                    onChange={(e) => setNewBook({ ...newBook, author: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter author name"
                    required
                  />
                </div>
                <div className="mb-6">
                  <label className="block text-gray-700 font-semibold mb-2">Reading Status</label>
                  <select
                    value={newBook.status}
                    onChange={(e) => setNewBook({ ...newBook, status: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="to_read">📋 Want to Read</option>
                    <option value="reading">📖 Currently Reading</option>
                    <option value="read">✅ Completed</option>
                  </select>
                </div>
                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-semibold transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex-1 bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg font-semibold transition-colors"
                  >
                    Add Book
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Books List */}
        <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl p-6 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-800">
              Your Reading List ({bookArray.length} books)
            </h2>
            <button
              onClick={loadBooks}
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors disabled:opacity-50"
            >
              {loading ? '🔄 Loading...' : '🔄 Refresh'}
            </button>
          </div>

          {loading && bookArray.length === 0 ? (
            <div className="text-center py-12">
              <div className="animate-spin text-4xl mb-4">📚</div>
              <p className="text-gray-600">Loading your books...</p>
            </div>
          ) : bookArray.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No books in your reading list yet</h3>
              <p className="text-gray-600 mb-6">Start building your reading list by adding your first book!</p>
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Add Your First Book
              </button>
            </div>
          ) : (
            Object.entries(statusConfig).map(([status, config]) => (
              <div key={status} className="mb-8">
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">{config.icon}</span>
                  <h3 className="text-lg font-semibold" style={{ color: config.color }}>
                    {config.title} ({groupedBooks[status].length})
                  </h3>
                </div>
                {groupedBooks[status].length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {groupedBooks[status].map(book => (
                      <div
                        key={book.uuid}
                        className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow border-l-4"
                        style={{ borderLeftColor: config.color }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-semibold text-gray-800 flex-1 mr-2">{book.title}</h4>
                          <button
                            onClick={() => deleteBook(book.uuid)}
                            className="text-red-500 hover:text-red-700 text-lg"
                            title="Delete book"
                          >
                            🗑️
                          </button>
                        </div>
                        <p className="text-gray-600 italic mb-3">by {book.author}</p>
                        <div className="mb-3">
                          <span
                            className="inline-block px-3 py-1 rounded-full text-sm font-medium"
                            style={{ backgroundColor: config.color + '20', color: config.color }}
                          >
                            {config.icon} {config.title}
                          </span>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Change Status:
                          </label>
                          <select
                            value={book.status}
                            onChange={(e) => updateBookStatus(book.uuid, e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="to_read">📋 Want to Read</option>
                            <option value="reading">📖 Currently Reading</option>
                            <option value="read">✅ Completed</option>
                          </select>
                        </div>
                        {book.created_at && (
                          <div className="mt-2 pt-2 border-t border-gray-200">
                            <small className="text-gray-500">
                              Added: {new Date(book.created_at).toLocaleDateString()}
                            </small>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500 italic">
                    No books in this category
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
