// Choreo Authentication Configuration
export const authConfig = {
  signInRedirectURL: process.env.REACT_APP_SIGN_IN_REDIRECT_URL || "http://localhost:3000",
  signOutRedirectURL: process.env.REACT_APP_SIGN_OUT_REDIRECT_URL || "http://localhost:3000",
  clientID: process.env.REACT_APP_CLIENT_ID || "your-client-id",
  baseUrl: process.env.REACT_APP_BASE_URL || "https://api.asgardeo.io/t/your-org",
  scope: ["openid", "profile", "email"],
  resourceServerURLs: [
    process.env.REACT_APP_API_URL || "http://localhost:8080"
  ],
  enablePKCE: true,
  storage: "webWorker"
};
