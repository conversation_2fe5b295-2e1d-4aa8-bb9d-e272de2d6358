# WSO2 Choreo Deployment Checklist

## Pre-Deployment Checklist

### Repository Setup
- [ ] Code is committed to a Git repository (GitHub, GitLab, etc.)
- [ ] Repository is accessible to WSO2 Choreo
- [ ] All configuration files are present:
  - [ ] `.choreo/component.yaml`
  - [ ] `.choreo/endpoints.yaml`
  - [ ] `service/Dockerfile`
  - [ ] `service/openapi.yaml`
  - [ ] `service/package.json`
  - [ ] `frontend/package.json`

### Database Setup
- [ ] MySQL database is accessible
- [ ] Database credentials are available:
  - [ ] Host: `mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com`
  - [ ] Port: `23009`
  - [ ] User: `avnadmin`
  - [ ] Password: `AVNS_RvXBNFm60TiBUEovKlt`
  - [ ] Database: `defaultdb`
- [ ] Database table will be created automatically by the application

### WSO2 Choreo Account
- [ ] WSO2 Choreo account is created and active
- [ ] Organization is set up in Choreo
- [ ] Project is created in Choreo Console

## Backend Deployment Steps

### 1. Create Backend Service Component
- [ ] Navigate to Choreo Console
- [ ] Click "Create Component"
- [ ] Select "Service" as component type
- [ ] Configure component:
  - [ ] Name: `reading-list-service`
  - [ ] Description: `Node.js REST API for reading list`
  - [ ] Repository URL: `<your-repository-url>`
  - [ ] Branch: `main`
  - [ ] Build Path: `./service`
  - [ ] Dockerfile Path: `./service/Dockerfile`

### 2. Configure Backend Environment Variables
- [ ] Go to "Configs & Secrets" section
- [ ] Add environment variables:
  - [ ] `PORT=8080`
  - [ ] `DB_HOST=mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com`
  - [ ] `DB_PORT=23009`
  - [ ] `DB_USER=avnadmin`
  - [ ] `DB_PASSWORD=AVNS_RvXBNFm60TiBUEovKlt`
  - [ ] `DB_NAME=defaultdb`

### 3. Deploy Backend Service
- [ ] Click "Deploy" button
- [ ] Wait for build to complete
- [ ] Verify deployment status is "Active"
- [ ] Note the service URL for frontend configuration
- [ ] Test health endpoint: `<service-url>/healthz`

## Frontend Deployment Steps

### 1. Create Frontend Web Application Component
- [ ] Click "Create Component"
- [ ] Select "Web Application" as component type
- [ ] Configure component:
  - [ ] Name: `reading-list-frontend`
  - [ ] Description: `React frontend for reading list`
  - [ ] Repository URL: `<your-repository-url>`
  - [ ] Branch: `main`
  - [ ] Build Path: `./frontend`
  - [ ] Build Command: `npm install && npm run build`
  - [ ] Publish Directory: `build`

### 2. Configure Frontend Environment Variables
- [ ] Add build-time environment variables:
  - [ ] `REACT_APP_CLIENT_ID=<choreo-client-id>`
  - [ ] `REACT_APP_BASE_URL=https://api.asgardeo.io/t/<org-name>`
  - [ ] `REACT_APP_SIGN_IN_REDIRECT_URL=<frontend-url>`
  - [ ] `REACT_APP_SIGN_OUT_REDIRECT_URL=<frontend-url>`
  - [ ] `REACT_APP_API_URL=<backend-service-url>`

### 3. Deploy Frontend Application
- [ ] Click "Deploy" button
- [ ] Wait for build to complete
- [ ] Verify deployment status is "Active"
- [ ] Note the frontend URL

## Authentication Setup

### 1. Configure Choreo Managed Authentication
- [ ] Go to project settings in Choreo Console
- [ ] Navigate to "Authentication" section
- [ ] Enable "Choreo Managed Authentication"
- [ ] Configure allowed redirect URLs:
  - [ ] Add frontend URL as allowed redirect
  - [ ] Add localhost URLs for development if needed
- [ ] Note the Client ID and Organization name

### 2. Update Authentication Configuration
- [ ] Update frontend environment variables with correct auth details
- [ ] Redeploy frontend if environment variables changed
- [ ] Verify authentication configuration in `frontend/src/config/auth-config.js`

## Connection Setup

### 1. Database Connection
- [ ] In Choreo Console, go to "Connections" tab
- [ ] Create new database connection:
  - [ ] Connection name: `mysql-reading-list`
  - [ ] Type: `MySQL`
  - [ ] Host: `mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com`
  - [ ] Port: `23009`
  - [ ] Database: `defaultdb`
  - [ ] Username: `avnadmin`
  - [ ] Password: `AVNS_RvXBNFm60TiBUEovKlt`
- [ ] Test connection
- [ ] Link connection to backend service

### 2. Frontend-Backend Connection
- [ ] Create connection from frontend to backend service
- [ ] Verify API URL is automatically configured
- [ ] Test connection between components

## Post-Deployment Testing

### 1. Application Testing
- [ ] Access frontend URL
- [ ] Test authentication flow:
  - [ ] Login functionality works
  - [ ] User information is displayed correctly
  - [ ] Logout functionality works
- [ ] Test book management features:
  - [ ] Add new book
  - [ ] View book list
  - [ ] Update book status
  - [ ] Delete book
- [ ] Test responsive design on mobile devices

### 2. API Testing
- [ ] Test all API endpoints:
  - [ ] `GET /reading-list/books` - List all books
  - [ ] `POST /reading-list/books` - Add new book
  - [ ] `GET /reading-list/books/{uuid}` - Get specific book
  - [ ] `PUT /reading-list/books/{uuid}` - Update book status
  - [ ] `DELETE /reading-list/books/{uuid}` - Delete book
  - [ ] `GET /healthz` - Health check

### 3. Error Handling Testing
- [ ] Test with invalid data
- [ ] Test network error scenarios
- [ ] Verify error messages are user-friendly
- [ ] Test authentication error handling

## Monitoring and Maintenance

### 1. Set Up Monitoring
- [ ] Configure Choreo monitoring dashboard
- [ ] Set up alerts for service failures
- [ ] Monitor API response times
- [ ] Track authentication metrics

### 2. Log Monitoring
- [ ] Check backend service logs
- [ ] Monitor frontend build logs
- [ ] Set up log aggregation if needed
- [ ] Configure log retention policies

## Troubleshooting Checklist

### Common Issues
- [ ] Authentication not working:
  - [ ] Verify Client ID and Base URL
  - [ ] Check redirect URLs
  - [ ] Verify CORS configuration
- [ ] Database connection issues:
  - [ ] Verify credentials
  - [ ] Check network connectivity
  - [ ] Verify SSL configuration
- [ ] API connection issues:
  - [ ] Check backend service status
  - [ ] Verify CORS settings
  - [ ] Check API URL configuration

### Debug Resources
- [ ] Browser developer tools for frontend issues
- [ ] Choreo logs for backend issues
- [ ] Network tab for API call debugging
- [ ] Choreo monitoring dashboard for performance issues

## Success Criteria

- [ ] Backend service is deployed and healthy
- [ ] Frontend application is deployed and accessible
- [ ] Authentication flow works correctly
- [ ] All CRUD operations function properly
- [ ] Database operations are working
- [ ] Application is responsive and user-friendly
- [ ] Error handling is working correctly
- [ ] Monitoring is set up and functional

## Final Notes

- Keep database credentials secure
- Regularly update dependencies
- Monitor application performance
- Set up backup procedures for database
- Document any custom configurations
- Plan for scaling if needed
