{"name": "reading-list-fullstack", "version": "1.0.0", "description": "Full-stack reading list application for WSO2 Choreo deployment", "main": "index.js", "scripts": {"start": "./start-local.sh", "test": "node test-full-stack.js", "install:backend": "cd service && npm install", "install:frontend": "cd frontend && npm install", "install:all": "npm run install:backend && npm run install:frontend", "build:frontend": "cd frontend && npm run build", "start:backend": "cd service && npm start", "start:frontend": "cd frontend && npx serve -s build -p 3000", "dev:backend": "cd service && npm run dev", "clean": "rm -rf service/node_modules frontend/node_modules frontend/build", "deploy:check": "node test-full-stack.js && echo 'Ready for Choreo deployment!'"}, "keywords": ["reading-list", "fullstack", "nodejs", "react", "mysql", "choreo", "wso2"], "author": "Your Name", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/your-username/reading-list-app.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {}, "dependencies": {}}