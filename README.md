# Reading List Full-Stack Application

A complete full-stack reading list application built with Node.js, React, and MySQL, designed for deployment on WSO2 Choreo platform with managed authentication.

## 🏗️ Architecture

- **Backend**: Node.js REST API with Express.js and MySQL
- **Frontend**: React application with Choreo managed authentication
- **Database**: MySQL (Aiven Cloud)
- **Authentication**: WSO2 Choreo managed authentication
- **Deployment**: WSO2 Choreo platform

## 📁 Project Structure

```
├── service/                 # Backend Node.js API
│   ├── app.mjs             # Express application
│   ├── cache.mjs           # Database connection and operations
│   ├── index.mjs           # Server entry point
│   ├── package.json        # Backend dependencies
│   ├── Dockerfile          # Backend container configuration
│   └── openapi.yaml        # API specification
├── frontend/               # React frontend application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API service layer
│   │   └── config/         # Authentication configuration
│   └── package.json        # Frontend dependencies
├── .choreo/               # Choreo deployment configuration
│   ├── component.yaml     # Component definitions
│   └── endpoints.yaml     # API endpoint configuration
└── README.md              # This file
```

## 🚀 Features

- **User Authentication**: Secure login/logout with Choreo managed authentication
- **Book Management**: Add, view, update, and delete books
- **Reading Status Tracking**: Track books as "Want to Read", "Reading", or "Completed"
- **Responsive UI**: Modern, mobile-friendly interface
- **Real-time Updates**: Instant UI updates after operations
- **Error Handling**: Comprehensive error handling and user feedback

## 🛠️ Local Development Setup

### Prerequisites

- Node.js 16+ and npm
- MySQL database access
- WSO2 Choreo account

### Backend Setup

1. Navigate to the service directory:
   ```bash
   cd service
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Update `.env` with your database credentials:
   ```env
   DB_HOST=mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com
   DB_PORT=23009
   DB_USER=avnadmin
   DB_PASSWORD=AVNS_RvXBNFm60TiBUEovKlt
   DB_NAME=defaultdb
   PORT=8080
   FRONTEND_URL=http://localhost:3000
   ```

5. Start the backend server:
   ```bash
   npm start
   ```

The backend will be available at `http://localhost:8080`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Update `.env` with your Choreo authentication details:
   ```env
   REACT_APP_CLIENT_ID=your-choreo-client-id
   REACT_APP_BASE_URL=https://api.asgardeo.io/t/your-org-name
   REACT_APP_SIGN_IN_REDIRECT_URL=http://localhost:3000
   REACT_APP_SIGN_OUT_REDIRECT_URL=http://localhost:3000
   REACT_APP_API_URL=http://localhost:8080
   ```

5. Start the frontend development server:
   ```bash
   npm start
   ```

The frontend will be available at `http://localhost:3000`

## 📊 Database Schema

The application uses a single `books` table:

```sql
CREATE TABLE books (
  uuid VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  author VARCHAR(255) NOT NULL,
  status ENUM('read', 'to_read', 'reading') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔌 API Endpoints

- `GET /reading-list/books` - Get all books
- `POST /reading-list/books` - Add a new book
- `GET /reading-list/books/{uuid}` - Get a specific book
- `PUT /reading-list/books/{uuid}` - Update book status
- `DELETE /reading-list/books/{uuid}` - Delete a book
- `GET /healthz` - Health check endpoint

## 🚀 WSO2 Choreo Deployment Guide

### Step 1: Prepare Your Repository

1. Ensure your code is in a Git repository (GitHub, GitLab, etc.)
2. Verify all configuration files are in place:
   - `.choreo/component.yaml`
   - `.choreo/endpoints.yaml`
   - `service/Dockerfile`
   - `service/openapi.yaml`

### Step 2: Create Choreo Project

1. Log in to [WSO2 Choreo Console](https://console.choreo.dev/)
2. Create a new project or select an existing one
3. Click "Create Component" to add your services

### Step 3: Deploy Backend Service

1. **Create Service Component**:
   - Component Type: `Service`
   - Name: `reading-list-service`
   - Description: `Node.js REST API for reading list`
   - GitHub Repository: Your repository URL
   - Branch: `main` (or your default branch)
   - Build Path: `./service`
   - Dockerfile Path: `./service/Dockerfile`

2. **Configure Environment Variables**:
   Go to the "Configs & Secrets" section and add:
   ```
   PORT=8080
   DB_HOST=mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com
   DB_PORT=23009
   DB_USER=avnadmin
   DB_PASSWORD=AVNS_RvXBNFm60TiBUEovKlt
   DB_NAME=defaultdb
   ```

3. **Deploy the Service**:
   - Click "Deploy" to build and deploy the backend
   - Wait for deployment to complete
   - Note the service URL for frontend configuration

### Step 4: Deploy Frontend Application

1. **Create Web Application Component**:
   - Component Type: `Web Application`
   - Name: `reading-list-frontend`
   - Description: `React frontend for reading list`
   - GitHub Repository: Your repository URL
   - Branch: `main`
   - Build Path: `./frontend`
   - Build Command: `npm install && npm run build`
   - Publish Directory: `build`

2. **Configure Environment Variables**:
   Add the following build-time environment variables:
   ```
   REACT_APP_CLIENT_ID=<your-choreo-client-id>
   REACT_APP_BASE_URL=https://api.asgardeo.io/t/<your-org-name>
   REACT_APP_SIGN_IN_REDIRECT_URL=<your-frontend-url>
   REACT_APP_SIGN_OUT_REDIRECT_URL=<your-frontend-url>
   REACT_APP_API_URL=<your-backend-service-url>
   ```

3. **Deploy the Frontend**:
   - Click "Deploy" to build and deploy the frontend
   - Wait for deployment to complete

### Step 5: Set Up Authentication

1. **Configure Choreo Authentication**:
   - Go to your project settings
   - Navigate to "Authentication" section
   - Enable "Choreo Managed Authentication"
   - Configure allowed redirect URLs
   - Note the Client ID and Organization details

2. **Update Frontend Configuration**:
   - Update the frontend environment variables with correct auth details
   - Redeploy the frontend if necessary

### Step 6: Configure Connections

1. **Backend to Database Connection**:
   - In Choreo Console, go to "Connections" tab
   - Create a new connection to your MySQL database
   - Configure connection details:
     - Host: `mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com`
     - Port: `23009`
     - Database: `defaultdb`
     - Username: `avnadmin`
     - Password: `AVNS_RvXBNFm60TiBUEovKlt`

2. **Frontend to Backend Connection**:
   - Create a connection from frontend to backend service
   - This will automatically configure the API URL
   - Update frontend environment variables if needed

### Step 7: Test the Application

1. **Access the Application**:
   - Open the frontend URL provided by Choreo
   - Test the authentication flow
   - Verify all CRUD operations work correctly

2. **Monitor and Debug**:
   - Use Choreo's monitoring dashboard
   - Check logs for any issues
   - Verify database connections

## 🔧 Troubleshooting

### Common Issues

1. **Authentication Issues**:
   - Verify Client ID and Base URL are correct
   - Check redirect URLs match exactly
   - Ensure CORS is properly configured

2. **Database Connection Issues**:
   - Verify database credentials
   - Check network connectivity
   - Ensure SSL configuration is correct

3. **API Connection Issues**:
   - Verify backend service is running
   - Check CORS configuration
   - Ensure API URLs are correct

### Debugging Tips

- Use browser developer tools to check network requests
- Check Choreo logs for backend errors
- Verify environment variables are set correctly
- Test API endpoints directly using tools like Postman

## 📝 License

This project is licensed under the Apache 2.0 License.
