#!/bin/bash

# Reading List App - Local Development Startup Script

echo "🚀 Starting Reading List Application Locally"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Waiting for $service_name to be ready...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s $url > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}⏳ Attempt $attempt/$max_attempts - waiting for $service_name...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start after $max_attempts attempts${NC}"
    return 1
}

# Check if required directories exist
if [ ! -d "service" ]; then
    echo -e "${RED}❌ Service directory not found. Please run this script from the project root.${NC}"
    exit 1
fi

if [ ! -d "frontend" ]; then
    echo -e "${RED}❌ Frontend directory not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Check if ports are available
echo -e "${BLUE}🔍 Checking port availability...${NC}"

if check_port 8080; then
    echo -e "${RED}❌ Port 8080 is already in use. Please stop the service using this port.${NC}"
    exit 1
fi

if check_port 3000; then
    echo -e "${RED}❌ Port 3000 is already in use. Please stop the service using this port.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Ports 8080 and 3000 are available${NC}"

# Install dependencies if needed
echo -e "${BLUE}📦 Checking dependencies...${NC}"

if [ ! -d "service/node_modules" ]; then
    echo -e "${YELLOW}Installing backend dependencies...${NC}"
    (cd service && npm install)
fi

if [ ! -d "frontend/node_modules" ]; then
    echo -e "${YELLOW}Installing frontend dependencies...${NC}"
    (cd frontend && npm install)
fi

# Build frontend if needed
if [ ! -d "frontend/build" ]; then
    echo -e "${YELLOW}Building frontend...${NC}"
    (cd frontend && npm run build)
fi

echo -e "${GREEN}✅ Dependencies ready${NC}"

# Start backend service
echo -e "${BLUE}🔧 Starting backend service...${NC}"
(cd service && npm start) &
BACKEND_PID=$!

# Wait for backend to be ready
if ! wait_for_service "http://localhost:8080/healthz" "Backend API"; then
    echo -e "${RED}❌ Failed to start backend service${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# Start frontend service
echo -e "${BLUE}🌐 Starting frontend service...${NC}"
(cd frontend && npx serve -s build -p 3000) &
FRONTEND_PID=$!

# Wait for frontend to be ready
if ! wait_for_service "http://localhost:3000" "Frontend Application"; then
    echo -e "${RED}❌ Failed to start frontend service${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 1
fi

# Run tests
echo -e "${BLUE}🧪 Running application tests...${NC}"
if node test-full-stack.js; then
    echo -e "${GREEN}✅ All tests passed!${NC}"
else
    echo -e "${RED}❌ Some tests failed. Check the output above.${NC}"
fi

# Display success message
echo ""
echo -e "${GREEN}🎉 Reading List Application is running successfully!${NC}"
echo "============================================="
echo -e "${BLUE}📱 Frontend:${NC} http://localhost:3000"
echo -e "${BLUE}🔧 Backend API:${NC} http://localhost:8080"
echo -e "${BLUE}📊 Health Check:${NC} http://localhost:8080/healthz"
echo ""
echo -e "${YELLOW}📝 Available API Endpoints:${NC}"
echo "  GET    /reading-list/books       - Get all books"
echo "  POST   /reading-list/books       - Add new book"
echo "  GET    /reading-list/books/:uuid - Get specific book"
echo "  PUT    /reading-list/books/:uuid - Update book status"
echo "  DELETE /reading-list/books/:uuid - Delete book"
echo ""
echo -e "${YELLOW}🛠️  Development Commands:${NC}"
echo "  Test API: curl http://localhost:8080/healthz"
echo "  Run tests: node test-full-stack.js"
echo "  Stop services: Press Ctrl+C"
echo ""
echo -e "${BLUE}📚 Next Steps:${NC}"
echo "  1. Open http://localhost:3000 in your browser"
echo "  2. Test the application functionality"
echo "  3. When ready, deploy to WSO2 Choreo using CHOREO_DEPLOYMENT_GUIDE.md"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Shutting down services...${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo -e "${GREEN}✅ Services stopped successfully${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Keep script running
echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
wait
