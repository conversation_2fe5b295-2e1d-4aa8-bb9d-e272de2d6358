# Choreo Component Configuration
schemaVersion: 1.0

# Backend Service Component
components:
  - name: reading-list-service
    type: service
    source:
      # Source code location
      path: ./service
    # Build configuration
    build:
      # Docker build context
      dockerfile: ./service/Dockerfile
    # Environment variables
    env:
      - name: PORT
        value: "8080"
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: host
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: port
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: user
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: password
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: database

  # Frontend Web App Component
  - name: reading-list-frontend
    type: web-application
    source:
      path: ./frontend
    # Build configuration
    build:
      commands:
        - npm install
        - npm run build
      buildPath: ./build
