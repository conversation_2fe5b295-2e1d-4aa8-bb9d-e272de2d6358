# Choreo Component Configuration
schemaVersion: 1.0

# Backend Service Component
components:
  - name: reading-list-service
    type: service
    source:
      # Source code location
      path: ./service
    # Build configuration
    build:
      # Docker build context
      dockerfile: ./service/Dockerfile
    # Environment variables
    env:
      - name: PORT
        value: "8080"
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: host
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: port
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: user
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: password
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            name: mysql-config
            key: database

  # Frontend Web App Component
  - name: reading-list-frontend
    type: web-application
    source:
      path: ./frontend
    # Build configuration
    build:
      commands:
        - npm install
        - npm run build
      buildPath: ./build
    # Environment variables for build
    env:
      - name: REACT_APP_CLIENT_ID
        valueFrom:
          secretKeyRef:
            name: auth-config
            key: client-id
      - name: REACT_APP_BASE_URL
        valueFrom:
          secretKeyRef:
            name: auth-config
            key: base-url
      - name: REACT_APP_API_URL
        valueFrom:
          connectionRef:
            name: reading-list-api
            key: serviceUrl
