#!/usr/bin/env node

// Full-stack application test script
const http = require('http');

const BACKEND_URL = 'http://localhost:8080';
const FRONTEND_URL = 'http://localhost:3000';

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = res.headers['content-type']?.includes('application/json') 
            ? JSON.parse(data) 
            : data;
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data, headers: res.headers });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testBackend() {
  console.log('🔧 Testing Backend API...');
  
  try {
    // Test health endpoint
    console.log('  ✓ Testing health endpoint...');
    const health = await makeRequest(`${BACKEND_URL}/healthz`);
    if (health.status !== 200) {
      throw new Error(`Health check failed: ${health.status}`);
    }
    console.log('    ✅ Health check passed');

    // Test get books (should be empty initially or have existing books)
    console.log('  ✓ Testing GET /reading-list/books...');
    const getBooks = await makeRequest(`${BACKEND_URL}/reading-list/books`);
    if (getBooks.status !== 200) {
      throw new Error(`Get books failed: ${getBooks.status}`);
    }
    console.log(`    ✅ Get books passed (${Object.keys(getBooks.data).length} books found)`);

    // Test add book
    console.log('  ✓ Testing POST /reading-list/books...');
    const newBook = {
      title: 'Test Book - Full Stack App',
      author: 'Test Author',
      status: 'to_read'
    };
    
    const addBook = await makeRequest(`${BACKEND_URL}/reading-list/books`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newBook)
    });
    
    if (addBook.status !== 201) {
      throw new Error(`Add book failed: ${addBook.status} - ${JSON.stringify(addBook.data)}`);
    }
    console.log('    ✅ Add book passed');
    
    const bookUuid = addBook.data.uuid;

    // Test get specific book
    console.log('  ✓ Testing GET /reading-list/books/:uuid...');
    const getBook = await makeRequest(`${BACKEND_URL}/reading-list/books/${bookUuid}`);
    if (getBook.status !== 200) {
      throw new Error(`Get specific book failed: ${getBook.status}`);
    }
    console.log('    ✅ Get specific book passed');

    // Test update book status
    console.log('  ✓ Testing PUT /reading-list/books/:uuid...');
    const updateBook = await makeRequest(`${BACKEND_URL}/reading-list/books/${bookUuid}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: 'reading' })
    });
    
    if (updateBook.status !== 200) {
      throw new Error(`Update book failed: ${updateBook.status}`);
    }
    console.log('    ✅ Update book status passed');

    // Test delete book
    console.log('  ✓ Testing DELETE /reading-list/books/:uuid...');
    const deleteBook = await makeRequest(`${BACKEND_URL}/reading-list/books/${bookUuid}`, {
      method: 'DELETE'
    });
    
    if (deleteBook.status !== 200) {
      throw new Error(`Delete book failed: ${deleteBook.status}`);
    }
    console.log('    ✅ Delete book passed');

    console.log('✅ Backend API tests completed successfully!\n');
    return true;
    
  } catch (error) {
    console.error('❌ Backend test failed:', error.message);
    return false;
  }
}

async function testFrontend() {
  console.log('🌐 Testing Frontend...');
  
  try {
    // Test frontend is serving
    console.log('  ✓ Testing frontend accessibility...');
    const frontend = await makeRequest(FRONTEND_URL);
    if (frontend.status !== 200) {
      throw new Error(`Frontend not accessible: ${frontend.status}`);
    }
    
    if (!frontend.data.includes('Reading List App')) {
      throw new Error('Frontend content does not include expected title');
    }
    console.log('    ✅ Frontend is accessible and serving correct content');

    // Test config.js is accessible
    console.log('  ✓ Testing config.js accessibility...');
    const config = await makeRequest(`${FRONTEND_URL}/config.js`);
    if (config.status !== 200) {
      throw new Error(`Config.js not accessible: ${config.status}`);
    }
    
    if (!config.data.includes('window.config')) {
      throw new Error('Config.js does not contain expected configuration');
    }
    console.log('    ✅ Config.js is accessible and contains configuration');

    console.log('✅ Frontend tests completed successfully!\n');
    return true;
    
  } catch (error) {
    console.error('❌ Frontend test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Full-Stack Application Tests\n');
  console.log('📋 Test Configuration:');
  console.log(`   Backend URL: ${BACKEND_URL}`);
  console.log(`   Frontend URL: ${FRONTEND_URL}\n`);
  
  const backendSuccess = await testBackend();
  const frontendSuccess = await testFrontend();
  
  console.log('📊 Test Results Summary:');
  console.log(`   Backend API: ${backendSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   Frontend: ${frontendSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (backendSuccess && frontendSuccess) {
    console.log('\n🎉 All tests passed! Your full-stack application is ready for deployment.');
    console.log('\n📝 Next Steps:');
    console.log('   1. Push your code to a Git repository');
    console.log('   2. Deploy to WSO2 Choreo following the deployment guide');
    console.log('   3. Enable Choreo-managed authentication');
    console.log('   4. Connect frontend to backend using Choreo connections');
    process.exit(0);
  } else {
    console.log('\n❌ Some tests failed. Please check the errors above and fix them before deployment.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}
