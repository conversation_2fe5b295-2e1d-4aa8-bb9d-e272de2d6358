# WSO2 Choreo Deployment Guide - Reading List App

## 🎯 Overview

This guide will walk you through deploying your full-stack Reading List application to WSO2 Choreo platform with managed authentication.

## ✅ Pre-Deployment Verification

Before deploying, ensure your application is working locally:

```bash
# Run the full-stack test
node test-full-stack.js
```

You should see all tests passing before proceeding.

## 📋 Phase 1: Repository Setup

1. **Push to Git Repository**:
   ```bash
   git init
   git add .
   git commit -m "Initial commit: Reading List full-stack app"
   git remote add origin <your-repository-url>
   git push -u origin main
   ```

2. **Verify Repository Structure**:
   ```
   ├── service/                 # Backend Node.js API
   ├── frontend/               # React frontend
   ├── .choreo/               # Choreo configuration
   ├── README.md
   ├── DEPLOYMENT_CHECKLIST.md
   └── test-full-stack.js
   ```

## 🚀 Phase 2: Deploy Backend Service

### Step 1: Create Backend Component

1. Log in to [WSO2 Choreo Console](https://console.choreo.dev/)
2. Create a new project or select existing one
3. Click **"+ Create Component"**
4. Configure:
   - **Component Type**: `Service`
   - **Name**: `reading-list-service`
   - **Description**: `Node.js REST API for reading list with MySQL`
   - **GitHub Repository**: Your repository URL
   - **Branch**: `main`
   - **Build Path**: `./service`
   - **Dockerfile Path**: `./service/Dockerfile`

### Step 2: Configure Environment Variables

Go to **"Configs & Secrets"** and add:

```env
PORT=8080
DB_HOST=mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com
DB_PORT=23009
DB_USER=avnadmin
DB_PASSWORD=AVNS_RvXBNFm60TiBUEovKlt
DB_NAME=defaultdb
```

### Step 3: Deploy Backend

1. Click **"Deploy"**
2. Wait for build and deployment to complete
3. Verify status shows **"Active"**
4. Note the service URL (e.g., `https://your-service-url.choreoapis.dev`)
5. Test the health endpoint: `<service-url>/healthz`

## 🌐 Phase 3: Deploy Frontend Application

### Step 1: Create Frontend Component

1. Click **"+ Create Component"** again
2. Configure:
   - **Component Type**: `Web Application`
   - **Name**: `reading-list-frontend`
   - **Description**: `React frontend for reading list`
   - **GitHub Repository**: Your repository URL
   - **Branch**: `main`
   - **Build Path**: `./frontend`
   - **Build Command**: `npm install && npm run build`
   - **Publish Directory**: `build`

### Step 2: Deploy Frontend

1. Click **"Deploy"**
2. Wait for build and deployment to complete
3. Note the frontend URL (e.g., `https://your-app-url.choreoapis.dev`)

## 🔐 Phase 4: Enable Authentication

### Step 1: Enable Choreo-Managed Authentication

1. Navigate to your **reading-list-frontend** component
2. Go to **"Settings"** tab
3. Find the **"Authentication"** card
4. Click **"Enable"**
5. Choose **"Choreo-Managed Identity Provider"**
6. Configure allowed redirect URLs (your frontend URL)

### Step 2: Test Authentication

1. Visit your frontend URL
2. You should be redirected to Choreo login page
3. Create account or sign in
4. Verify you're redirected back to your app

## 🔗 Phase 5: Connect Frontend to Backend

### Step 1: Create Connection

1. In your **reading-list-frontend** component
2. Go to **"Dependencies"** → **"Connections"**
3. Find your **reading-list-api** backend service
4. Click **"Connect"**
5. Choreo will create `CHOREO_API_URL` environment variable

### Step 2: Configure Frontend to Use Connection

1. Go to **"Configs & Secrets"** for your frontend
2. Click **"Create"** → **"ConfigMap"**
3. Select **"Mount as File"**
4. **Mount Path**: `/usr/share/nginx/html/config.js`
5. **Content**:
   ```javascript
   window.config = {
       backendUrl: "${CHOREO_API_URL}"
   };
   ```
6. Click **"Save"**
7. **Redeploy** your frontend component

## 🧪 Phase 6: Test Your Application

### Step 1: Access Application

1. Visit your frontend URL
2. Complete authentication flow
3. Test all features:
   - ✅ Add new books
   - ✅ View book list
   - ✅ Update book status
   - ✅ Delete books

### Step 2: Verify Backend Connection

1. Check browser developer tools → Network tab
2. Verify API calls are going to your backend service
3. Confirm no CORS errors
4. Test all CRUD operations

## 📊 Phase 7: Monitor and Maintain

### Monitoring

1. Use Choreo's monitoring dashboard
2. Check service health and performance
3. Monitor API response times
4. Track authentication metrics

### Logs

1. Access logs through Choreo console
2. Monitor for errors or issues
3. Set up alerts if needed

## 🔧 Troubleshooting

### Common Issues

1. **Authentication not working**:
   - Verify redirect URLs are correct
   - Check authentication is enabled
   - Ensure frontend is properly configured

2. **Backend connection issues**:
   - Verify connection is created
   - Check config.js is properly mounted
   - Ensure CHOREO_API_URL is available

3. **Database connection issues**:
   - Verify environment variables are set
   - Check database credentials
   - Ensure network connectivity

### Debug Steps

1. Check Choreo logs for both components
2. Use browser developer tools
3. Verify environment variables
4. Test API endpoints directly

## ✅ Success Criteria

- [ ] Backend service deployed and healthy
- [ ] Frontend application accessible
- [ ] Authentication working correctly
- [ ] All CRUD operations functional
- [ ] No console errors
- [ ] Responsive design working

## 🎉 Congratulations!

Your full-stack Reading List application is now deployed on WSO2 Choreo with:

- ✅ **Secure Authentication**: Choreo-managed identity provider
- ✅ **Scalable Backend**: Node.js API with MySQL database
- ✅ **Modern Frontend**: React application with Tailwind CSS
- ✅ **Service Connections**: Automatic service discovery
- ✅ **Production Ready**: Monitoring, logging, and error handling

## 📚 Additional Resources

- [WSO2 Choreo Documentation](https://wso2.com/choreo/docs/)
- [Choreo Samples Repository](https://github.com/wso2/choreo-samples)
- [React Documentation](https://reactjs.org/docs/)
- [Node.js Best Practices](https://nodejs.org/en/docs/guides/)
