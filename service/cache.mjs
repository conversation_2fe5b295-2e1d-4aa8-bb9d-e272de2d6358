import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'mysql-c1445d93c5f643a78c716f1fc597b32e-mysqldat2513987829-chore.l.aivencloud.com',
  port: process.env.DB_PORT || 23009,
  user: process.env.DB_USER || 'avnadmin',
  password: process.env.DB_PASSWORD || 'AVNS_RvXBNFm60TiBUEovKlt',
  database: process.env.DB_NAME || 'defaultdb',
  ssl: {
    rejectUnauthorized: false
  }
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Initialize database table
async function initializeDatabase() {
  try {
    const connection = await pool.getConnection();

    // Create books table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS books (
        uuid VARCHAR(36) PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        author VARCHA<PERSON>(255) NOT NULL,
        status ENUM('read', 'to_read', 'reading') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    connection.release();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
}

// Database operations
const db = {
  async set(uuid, book) {
    try {
      const { title, author, status } = book;
      await pool.execute(
        'INSERT INTO books (uuid, title, author, status) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE title = ?, author = ?, status = ?',
        [uuid, title, author, status, title, author, status]
      );
      return true;
    } catch (error) {
      console.error('Error setting book:', error);
      throw error;
    }
  },

  async get(uuid) {
    try {
      const [rows] = await pool.execute('SELECT * FROM books WHERE uuid = ?', [uuid]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting book:', error);
      throw error;
    }
  },

  async has(uuid) {
    try {
      const [rows] = await pool.execute('SELECT uuid FROM books WHERE uuid = ?', [uuid]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error checking book existence:', error);
      throw error;
    }
  },

  async keys() {
    try {
      const [rows] = await pool.execute('SELECT uuid FROM books');
      return rows.map(row => row.uuid);
    } catch (error) {
      console.error('Error getting book keys:', error);
      throw error;
    }
  },

  async getAll() {
    try {
      const [rows] = await pool.execute('SELECT * FROM books');
      const result = {};
      rows.forEach(row => {
        result[row.uuid] = row;
      });
      return result;
    } catch (error) {
      console.error('Error getting all books:', error);
      throw error;
    }
  },

  async del(uuid) {
    try {
      await pool.execute('DELETE FROM books WHERE uuid = ?', [uuid]);
      return true;
    } catch (error) {
      console.error('Error deleting book:', error);
      throw error;
    }
  }
};

// Initialize database on module load
initializeDatabase().catch(console.error);

export default db;